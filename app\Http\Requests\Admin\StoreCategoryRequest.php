<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
        'name.ar'          => 'required|string|max:255',
        'name.en'          => 'required|string|max:255',
        'slug'             => 'required|string|max:255|unique:categories,slug',
        'description.ar'   => 'required|string',
        'description.en'   => 'required|string',
        'is_showing'       => 'nullable|boolean',
        'is_popular'       => 'nullable|boolean',
        'image'            => 'required|image|mimes:jpg,jpeg,png,gif,webp|max:2048',
        'meta_title.ar'    => 'required|string|max:255',
        'meta_title.en'    => 'required|string|max:255',
        'meta_description.ar' => 'required|string|max:500',
        'meta_description.en' => 'required|string|max:500',
        'meta_keywords.ar' => 'nullable|string|max:255',
        'meta_keywords.en' => 'nullable|string|max:255',
        ];
    }
}
