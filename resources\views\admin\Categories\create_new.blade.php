@extends('admin.layouts.master')
@section('TitlePage', __('admin.add_category'))
@section('content')
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>{{ __('admin.add_category') }}</h4>
            <h6>{{ __('admin.create_category') }}</h6>
        </div>
        <div class="page-btn">
            <a href="{{route('categories.index')}}" class="btn btn-added">
                <img src="{{asset('admin/assets/img/icons/reverse.svg')}}" alt="img" class="me-1">{{ __('admin.category_list') }}
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('categories.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- Basic Information -->
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-3 border-bottom pb-2">{{ __('admin.category_information') }}</h4>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label class="form-label">{{ __('admin.category_name_ar') }} <span class="text-danger">&#9913;</span></label>
                            <input type="text" name="name[ar]" class="form-control" placeholder="{{ __('admin.name_in_arabic') }}" value="{{ old('name.ar') }}" required>
                            @error('name.ar')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label class="form-label">{{ __('admin.category_name_en') }} <span class="text-danger">&#9913;</span></label>
                            <input type="text" name="name[en]" class="form-control" placeholder="{{ __('admin.name_in_english') }}" value="{{ old('name.en') }}" required>
                            @error('name.en')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>{{ __('admin.category_description_ar') }} <span class="text-danger">&#9913;</span></label>
                            <textarea class="form-control" name="description[ar]" id="summernote_ar" rows="4" placeholder="{{ __('admin.category_desc_arabic') }}">{{ old('description.ar') }}</textarea>
                            @error('description.ar')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>{{ __('admin.category_description_en') }} <span class="text-danger">&#9913;</span></label>
                            <textarea class="form-control" name="description[en]" id="summernote_en" rows="4" placeholder="{{ __('admin.category_desc_english') }}">{{ old('description.en') }}</textarea>
                            @error('description.en')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Category Settings -->
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label for="is_showing">{{ __('admin.show_in_website') }}</label>
                            <select name="is_showing" class="form-control">
                                <option value="1" {{ old('is_showing') == 1 ? 'selected' : '' }}>{{ __('admin.yes') }}</option>
                                <option value="0" {{ old('is_showing') == 0 ? 'selected' : '' }}>{{ __('admin.no') }}</option>
                            </select>
                            @error('is_showing')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label for="is_popular">{{ __('admin.popular_in_website') }}</label>
                            <select name="is_popular" class="form-control">
                                <option value="1" {{ old('is_popular') == 1 ? 'selected' : '' }}>{{ __('admin.yes') }}</option>
                                <option value="0" {{ old('is_popular') == 0 ? 'selected' : '' }}>{{ __('admin.no') }}</option>
                            </select>
                            @error('is_popular')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Category Image -->
                    <div class="col-lg-12">
                        <div class="form-group">
                            <label>{{ __('admin.category_image') }} <span class="text-danger">{{ __('admin.must_have_photo') }} &#9913;</span></label>
                            <div class="image-upload">
                                <input class="form-control" type="file" name="image" required>
                                <div class="image-uploads">
                                    <img src="{{ asset('admin/assets/img/icons/upload.svg') }}" alt="img">
                                    <h4>{{ __('admin.drag_drop_upload') }}</h4>
                                </div>
                            </div>
                            @error('image')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- SEO Information -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4 class="mb-3 border-bottom pb-2">{{ __('admin.seo_category') }}</h4>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.slug') }} <span class="text-danger">&#9913;</span></label>
                            <div class="col-lg-9">
                                <input type="text" name="slug" class="form-control" placeholder="{{ __('admin.slug') }}" value="{{ old('slug') }}" required>
                                @error('slug')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_title_ar') }} <span class="text-danger">&#9913;</span></label>
                            <div class="col-lg-9">
                                <input type="text" name="meta_title[ar]" class="form-control" placeholder="{{ __('admin.meta_title_arabic') }}" value="{{ old('meta_title.ar') }}" required>
                                @error('meta_title.ar')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_title_en') }} <span class="text-danger">&#9913;</span></label>
                            <div class="col-lg-9">
                                <input type="text" name="meta_title[en]" class="form-control" placeholder="{{ __('admin.meta_title_english') }}" value="{{ old('meta_title.en') }}" required>
                                @error('meta_title.en')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_description_ar') }} <span class="text-danger">&#9913;</span></label>
                            <div class="col-lg-9">
                                <textarea name="meta_description[ar]" class="form-control" rows="2" placeholder="{{ __('admin.meta_desc_arabic') }}" required>{{ old('meta_description.ar') }}</textarea>
                                @error('meta_description.ar')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_description_en') }} <span class="text-danger">&#9913;</span></label>
                            <div class="col-lg-9">
                                <textarea name="meta_description[en]" class="form-control" rows="2" placeholder="{{ __('admin.meta_desc_english') }}" required>{{ old('meta_description.en') }}</textarea>
                                @error('meta_description.en')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_keywords_ar') }}</label>
                            <div class="col-lg-9">
                                <textarea name="meta_keywords[ar]" class="form-control" rows="3" placeholder="{{ __('admin.meta_keywords_arabic') }}">{{ old('meta_keywords.ar') }}</textarea>
                                @error('meta_keywords.ar')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-3 col-form-label">{{ __('admin.meta_keywords_en') }}</label>
                            <div class="col-lg-9">
                                <textarea name="meta_keywords[en]" class="form-control" rows="3" placeholder="{{ __('admin.meta_keywords_english') }}">{{ old('meta_keywords.en') }}</textarea>
                                @error('meta_keywords.en')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="row mt-4">
                    <div class="col-lg-12 text-center">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fa fa-save me-2"></i> {{ __('admin.create') }}
                        </button>
                        <a href="{{route('categories.index')}}" class="btn btn-secondary">
                            <i class="fa fa-times me-2"></i> {{ __('admin.cancel') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Summernote editors
        $('#summernote_ar').summernote({
            height: 150,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview']]
            ],
            placeholder: '{{ __("admin.category_desc_arabic") }}'
        });

        $('#summernote_en').summernote({
            height: 150,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview']]
            ],
            placeholder: '{{ __("admin.category_desc_english") }}'
        });
    });
</script>
@endsection
